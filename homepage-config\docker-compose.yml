services:
  homepage:
    image: ghcr.io/gethomepage/homepage:latest
    container_name: homepage
    ports:
      - 3000:3000
    volumes:
      - ./homepage-config:/app/config
    restart: unless-stopped

  shiori:
    image: ghcr.io/go-shiori/shiori:latest
    container_name: shiori
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - "C:/Users/<USER>/Desktop/shiori-data:/srv/shiori"
    command: server

  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: cloudflared
    restart: unless-stopped
    command: tunnel --no-autoupdate run --token eyJhIjoiMTMwMTZiMWJkY2MwMDlmOTNkN2U3YzdiNWQ3MGNlNWUiLCJ0IjoiY2ExNjJiNzUtMDc3OC00YTMzLWEwMzktNTUwOTAwNWFmNjQxIiwicyI6Ik1HSXlObU15WXpFdE0yUTNOaTAwTURVMkxXSTNNVGN0WTJGa1pUWmlZekE5WXpCayJ9